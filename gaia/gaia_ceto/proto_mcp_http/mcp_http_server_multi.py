#!/usr/bin/env python3
"""
Gaia Aggregate MCP HTTP Server - Final clean implementation

A production-ready MCP server that supports:
- Local tools with full FastMCP features
- Third-party MCP servers (URL-based and process-spawned)
- Parameter mapping and default parameters
- SSE and HTTP protocols
- Clean error handling and logging
- Stable async context management
"""

import os
import sys
import json
import asyncio
import logging
import argparse
from typing import Dict, List, Any
from contextlib import asynccontextmanager

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))))

from mcp.server.fastmcp import FastMCP

# Import shared tools from the common module
from gaia.gaia_ceto.proto_mcp.mcp_tools import (
    echostring,
    echostring_table,
    long_task,
    firecrawl_scrape_text_only,
)

# Import multi-MCP client with error handling
try:
    from multi_mcp_client_clean import MultiMCPClient
except ImportError:
    print("Error: multi_mcp_client.py not found in the same directory")
    sys.exit(1)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class MCPServerMulti:
    """Gaia Aggregate MCP server with local and third-party tool support."""

    def __init__(self, config_file: str = "server_config.json"):
        self.config_file = config_file
        # Initialize FastMCP with proper lifespan management
        self.mcp = FastMCP("gaia_aggregate_server", lifespan=self.lifespan)
        self.third_party_client = MultiMCPClient()
        self.server_configs: Dict[str, Dict[str, Any]] = {}

        # Register built-in tools
        self._register_builtin_tools()

    @asynccontextmanager
    async def lifespan(self, app):
        """Lifespan context manager for proper async resource management."""
        # Startup
        logger.info("Starting up MCP server...")
        await self.initialize()
        yield
        # Shutdown
        logger.info("Shutting down MCP server...")
        await self.cleanup()
    
    def _register_builtin_tools(self):
        """Register built-in local tools."""

        # Register shared tools from mcp_tools module
        self.mcp.add_tool(echostring)
        self.mcp.add_tool(echostring_table)
        self.mcp.add_tool(long_task)
        self.mcp.add_tool(firecrawl_scrape_text_only)

        @self.mcp.tool()
        async def echo(message: str) -> str:
            """Echo a message back to test the server."""
            return f"Echo: {message}"

        @self.mcp.tool()
        async def server_info() -> str:
            """Get comprehensive server information."""
            info = ["Gaia Aggregate Server - Information", "=" * 40, ""]
            info.append(f"Config file: {self.config_file}")
            info.append(f"Third-party servers configured: {len(self.server_configs)}")
            
            # List configured servers
            if self.server_configs:
                info.append("\nConfigured servers:")
                for server_id, config in self.server_configs.items():
                    enabled = "✅" if config.get('enabled', True) else "❌"
                    server_type = "URL" if 'url' in config else "Process"
                    protocol = config.get('protocol', 'N/A')
                    namespace = config.get('namespace', server_id)
                    info.append(f"  {enabled} {server_id} ({server_type}/{protocol}) -> {namespace}__*")
            
            # List active connections
            if hasattr(self.third_party_client, 'connections'):
                active_connections = [k for k, v in self.third_party_client.connections.items() if v]
                info.append(f"\nActive connections: {len(active_connections)}")
                for conn_id in active_connections:
                    connection = self.third_party_client.connections[conn_id]
                    tool_count = len(connection.get('tools', []))
                    info.append(f"  ✅ {conn_id} ({tool_count} tools)")
            
            return "\n".join(info)
        
        @self.mcp.tool()
        async def list_all_tools() -> str:
            """List all available tools (built-in and third-party)."""
            tools = ["Available Tools", "=" * 20, ""]
            
            # Built-in tools
            tools.append("Built-in tools:")
            tools.extend([
                "  • echostring",
                "  • echostring_table",
                "  • long_task",
                "  • firecrawl_scrape_text_only",
                "  • echo",
                "  • server_info",
                "  • list_all_tools"
            ])
            
            # Third-party tools
            if hasattr(self.third_party_client, 'connections'):
                for server_id, connection in self.third_party_client.connections.items():
                    if connection and connection.get('tools'):
                        namespace = self.server_configs.get(server_id, {}).get('namespace', server_id)
                        tools.append(f"\n{server_id} tools (namespace: {namespace}):")
                        for tool in connection['tools']:
                            tools.append(f"  • {namespace}__{tool['name']}")
            
            return "\n".join(tools)
    
    async def load_configuration(self):
        """Load and validate server configuration."""
        if not os.path.exists(self.config_file):
            logger.warning(f"Configuration file {self.config_file} not found - running with built-in tools only")
            return
        
        try:
            with open(self.config_file, 'r') as f:
                config = json.load(f)
            
            self.server_configs = config.get('mcpServers', {})
            enabled_count = sum(1 for cfg in self.server_configs.values() if cfg.get('enabled', True))
            logger.info(f"Loaded configuration: {enabled_count}/{len(self.server_configs)} servers enabled")
            
        except Exception as e:
            logger.error(f"Failed to load configuration: {e}")
            self.server_configs = {}
    
    async def connect_third_party_servers(self):
        """Connect to all enabled third-party servers."""
        if not self.server_configs:
            logger.info("No third-party servers configured")
            return
        
        success_count = 0
        for server_id, config in self.server_configs.items():
            if not config.get('enabled', True):
                logger.debug(f"Skipping disabled server: {server_id}")
                continue
            
            try:
                if await self._connect_single_server(server_id, config):
                    await self._register_server_tools(server_id, config)
                    success_count += 1
                    logger.info(f"✅ Connected to {server_id}")
                else:
                    logger.error(f"❌ Failed to connect to {server_id}")
            
            except Exception as e:
                logger.error(f"❌ Error with {server_id}: {e}")
        
        logger.info(f"Third-party servers: {success_count} connected successfully")
    
    async def _connect_single_server(self, server_id: str, config: Dict[str, Any]) -> bool:
        """Connect to a single third-party server."""
        
        if 'url' in config:
            # URL-based server (hosted)
            url = self._resolve_env_variables(config['url'])
            protocol = config.get('protocol', 'sse')
            description = config.get('description', f'{server_id} hosted server')
            
            return await self.third_party_client.add_server(
                server_id=server_id,
                server_url=url,
                protocol=protocol,
                description=description
            )
        
        elif 'command' in config:
            # Process-spawned server
            command = config['command']
            args = config.get('args', [])
            env = {k: self._resolve_env_variables(v) for k, v in config.get('env', {}).items()}
            description = config.get('description', f'{server_id} process server')
            
            return await self.third_party_client.add_server_process(
                server_id=server_id,
                command=command,
                args=args,
                env=env,
                description=description
            )
        
        else:
            logger.error(f"Invalid configuration for {server_id}: missing 'url' or 'command'")
            return False
    
    def _resolve_env_variables(self, value: str) -> str:
        """Resolve environment variables in configuration values."""
        if isinstance(value, str) and '{' in value and '}' in value:
            try:
                return value.format(**os.environ)
            except KeyError as e:
                logger.error(f"Environment variable not found: {e}")
                return value
        return value
    
    async def _register_server_tools(self, server_id: str, config: Dict[str, Any]):
        """Register all tools from a connected third-party server."""
        connection = self.third_party_client.connections.get(server_id)
        if not connection or not connection.get('tools'):
            logger.warning(f"No tools found for {server_id}")
            return
        
        namespace = config.get('namespace', server_id)
        param_mappings = config.get('parameterMapping', {})
        default_params = config.get('defaultParameters', {})
        
        for tool_info in connection['tools']:
            tool_name = tool_info['name']
            namespaced_name = f"{namespace}__{tool_name}"
            
            # Create delegated tool with proper closure
            delegated_tool = self._create_delegated_tool(
                server_id, tool_name, namespaced_name, tool_info,
                param_mappings.get(tool_name, {}),
                default_params.get(tool_name, {})
            )
            
            # Register with FastMCP
            self.mcp.tool()(delegated_tool)
            logger.debug(f"Registered: {namespaced_name}")
        
        tool_count = len(connection['tools'])
        logger.info(f"Registered {tool_count} tools from {server_id} with namespace '{namespace}'")
    
    def _create_delegated_tool(self, server_id: str, tool_name: str, namespaced_name: str, 
                              tool_info: Dict[str, Any], param_mapping: Dict[str, str], 
                              default_params: Dict[str, Any]):
        """Create a delegated tool function with proper parameter handling."""
        
        async def delegated_tool(**kwargs):
            logger.debug(f"Delegated tool {namespaced_name} called with kwargs: {kwargs}")

            # Handle FastMCP parameter wrapping
            if len(kwargs) == 1 and 'kwargs' in kwargs:
                actual_params = kwargs['kwargs']
                logger.debug(f"Unwrapped kwargs for {namespaced_name}: {actual_params}")
            else:
                actual_params = kwargs

            # Apply parameter mapping and defaults
            final_params = default_params.copy()
            for param_name, param_value in actual_params.items():
                mapped_name = param_mapping.get(param_name, param_name)
                final_params[mapped_name] = param_value

            logger.debug(f"Final params for {namespaced_name}: {final_params}")

            # Call third-party tool with improved error handling and context management
            try:
                logger.info(f"Calling {tool_name} on {server_id}")

                # Use asyncio.create_task to ensure proper context management
                task = asyncio.create_task(
                    self.third_party_client.call_tool(
                        server_id=server_id,
                        tool_name=tool_name,
                        tool_input=final_params,
                        tool_call_id=f"call_{server_id}_{tool_name}"
                    )
                )

                # Wait for the task with timeout but don't shield it
                result = await asyncio.wait_for(task, timeout=60.0)

                logger.info(f"Tool {tool_name} result: success={result.get('success', False)}")

                if result.get('success'):
                    content = result.get('content', [])
                    formatted_result = self._format_tool_result(content)
                    logger.debug(f"Formatted result length: {len(formatted_result)} chars")
                    return formatted_result
                else:
                    error_msg = result.get('error', 'Unknown error')
                    logger.error(f"Tool {tool_name} reported failure: {error_msg}")
                    raise Exception(f"Tool {tool_name} failed: {error_msg}")

            except asyncio.TimeoutError:
                logger.error(f"Tool {namespaced_name} timed out after 60 seconds")
                raise Exception(f"Tool {namespaced_name} timed out")
            except asyncio.CancelledError:
                logger.error(f"Tool {namespaced_name} was cancelled")
                raise Exception(f"Tool {namespaced_name} was cancelled")
            except Exception as e:
                logger.error(f"Error calling {namespaced_name}: {e}")
                logger.error(f"Exception type: {type(e).__name__}")
                logger.error(f"Exception details: {repr(e)}")
                import traceback
                logger.error(f"Traceback: {traceback.format_exc()}")
                raise Exception(f"Failed to call {namespaced_name}: {str(e)}")
        
        # Set function metadata
        delegated_tool.__name__ = namespaced_name
        delegated_tool.__doc__ = tool_info.get('description', f'Third-party tool: {tool_name}')
        
        return delegated_tool
    
    def _format_tool_result(self, content: List[Any], max_chars: int = 50000) -> str:
        """Format tool result content for display with size limits."""
        if not content:
            return "No content returned"

        # Handle single text content
        if len(content) == 1 and hasattr(content[0], 'text'):
            result = content[0].text
        else:
            # Handle multiple content items
            result_parts = []
            for item in content:
                if hasattr(item, 'text'):
                    result_parts.append(item.text)
                elif hasattr(item, 'data'):
                    result_parts.append(str(item.data))
                elif isinstance(item, str):
                    result_parts.append(item)
                elif isinstance(item, dict):
                    # Handle dictionary content (common in API responses)
                    import json
                    result_parts.append(json.dumps(item, indent=2))
                else:
                    # Fallback to string representation
                    result_parts.append(str(item))

            result = "\n\n".join(result_parts) if result_parts else "No content returned"

        # Truncate if too long to prevent token overflow
        if len(result) > max_chars:
            truncated = result[:max_chars]
            # Try to truncate at a word boundary
            last_space = truncated.rfind(' ')
            if last_space > max_chars * 0.8:  # Only if we don't lose too much
                truncated = truncated[:last_space]
            result = truncated + f"\n\n[Content truncated - showing first {len(truncated)} characters of {len(result)} total]"

        return result
    
    async def initialize(self):
        """Initialize the complete server."""
        logger.info("Initializing Gaia Aggregate Server...")
        await self.load_configuration()
        await self.connect_third_party_servers()
        logger.info("Gaia Aggregate Server initialization finished")
    
    async def cleanup(self):
        """Clean up all resources."""
        logger.info("Cleaning up Gaia Aggregate Server...")
        if self.third_party_client:
            await self.third_party_client.cleanup()
        logger.info("Cleanup complete")
    
    async def run_server(self, host: str = "0.0.0.0", port: int = 9000):
        """Run the complete server with proper async lifecycle."""
        logger.info(f"Starting Gaia Aggregate Server on {host}:{port}")

        try:
            # Create and run the server - initialization handled by lifespan
            app = self.mcp.streamable_http_app()

            import uvicorn
            config = uvicorn.Config(
                app=app,
                host=host,
                port=port,
                log_level="info",
                access_log=False
            )

            server = uvicorn.Server(config)
            await server.serve()

        except Exception as e:
            logger.error(f"Server error: {e}")
            raise


def main():
    """Main entry point with argument parsing."""
    parser = argparse.ArgumentParser(description="Complete MCP HTTP Server")
    parser.add_argument("--host", default="0.0.0.0", help="Host to bind to (default: 0.0.0.0)")
    parser.add_argument("--port", type=int, default=9000, help="Port to bind to (default: 9000)")
    parser.add_argument("--config", default="server_config.json", help="Configuration file (default: server_config.json)")
    parser.add_argument("--debug", action="store_true", help="Enable debug logging")
    
    args = parser.parse_args()
    
    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Create and run server
    server = MCPServerMulti(args.config)
    
    try:
        asyncio.run(server.run_server(args.host, args.port))
    except KeyboardInterrupt:
        logger.info("Server stopped by user")
    except Exception as e:
        logger.error(f"Server failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
